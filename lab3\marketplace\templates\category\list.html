{% extends 'base.html' %}

{% block title %}Categories - MarketPlace{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title">
            <i class="fas fa-tags"></i> Product Categories
        </h1>
        
        <!-- Search and Add Category Section -->
        <div class="row mb-4">
            <div class="col-md-8">
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" 
                           placeholder="Search categories by name or description..." 
                           value="{{ search_query|default:'' }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'category:create' %}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add New Category
                </a>
            </div>
        </div>
        
        <!-- Categories Count -->
        <div class="mb-3">
            <p class="text-muted">
                <i class="fas fa-info-circle"></i> 
                Showing {{ total_categories }} categor{{ total_categories|pluralize:"y,ies" }}
                {% if search_query %}for "{{ search_query }}"{% endif %}
            </p>
        </div>
        
        <!-- Categories Grid -->
        {% if categories %}
            <div class="row">
                {% for category in categories %}
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card h-100 hover-scale">
                            {% if category.image %}
                                <img src="{{ category.image.url }}" class="card-img-top" 
                                     style="height: 180px; object-fit: cover;" alt="{{ category.name }}">
                            {% else %}
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-gradient" 
                                     style="height: 180px; background: linear-gradient(45deg, #6366f1, #8b5cf6);">
                                    <i class="fas fa-tag fa-3x text-white"></i>
                                </div>
                            {% endif %}
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ category.name }}</h5>
                                <p class="card-text flex-grow-1">
                                    {{ category.description|truncatewords:12 }}
                                </p>
                                
                                <div class="mb-3">
                                    <span class="badge bg-info">
                                        <i class="fas fa-box"></i> {{ category.get_products_count }} product{{ category.get_products_count|pluralize }}
                                    </span>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="{% url 'category:detail' category.pk %}" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ category.created_at|date:"M d, Y" }}
                                    </small>
                                    <div>
                                        <a href="{% url 'category:update' category.pk %}" class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'category:delete' category.pk %}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="Categories pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i> First
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    <i class="fas fa-angle-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    Next <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    Last <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">No Categories Found</h3>
                {% if search_query %}
                    <p class="text-muted">No categories match your search for "{{ search_query }}"</p>
                    <a href="{% url 'category:list' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> View All Categories
                    </a>
                {% else %}
                    <p class="text-muted">Start by creating your first category!</p>
                    <a href="{% url 'category:create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Create First Category
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
