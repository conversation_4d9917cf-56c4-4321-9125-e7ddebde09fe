{% extends 'base.html' %}

{% block title %}{{ category.name }} - MarketPlace{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'category:list' %}" class="text-decoration-none">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
            </ol>
        </nav>
        
        <!-- Category Header -->
        <div class="row mb-4">
            <div class="col-lg-4 mb-3">
                <div class="card">
                    {% if category.image %}
                        <img src="{{ category.image.url }}" class="card-img-top" 
                             style="height: 250px; object-fit: cover;" alt="{{ category.name }}">
                    {% else %}
                        <div class="card-img-top d-flex align-items-center justify-content-center" 
                             style="height: 250px; background: linear-gradient(45deg, #6366f1, #8b5cf6);">
                            <i class="fas fa-tag fa-5x text-white"></i>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-body">
                        <h1 class="card-title gradient-text">{{ category.name }}</h1>
                        
                        <!-- Category Stats -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h3 class="text-primary">{{ products_count }}</h3>
                                    <small class="text-muted">Product{{ products_count|pluralize }}</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h3 class="text-success">{{ category.created_at|date:"M Y" }}</h3>
                                    <small class="text-muted">Created</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h3 class="text-info">{{ category.updated_at|date:"M d" }}</h3>
                                    <small class="text-muted">Last Updated</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Description -->
                        <div class="mb-4">
                            <h5><i class="fas fa-info-circle"></i> Description</h5>
                            <p class="text-muted">{{ category.description|linebreaks }}</p>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="{% url 'category:list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Categories
                            </a>
                            <a href="{% url 'category:update' category.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Category
                            </a>
                            <a href="{% url 'category:delete' category.pk %}" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete Category
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Products in this Category -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-box"></i> Products in {{ category.name }}
                    <span class="badge bg-primary ms-2">{{ products_count }}</span>
                </h3>
            </div>
            
            <div class="card-body">
                {% if page_obj %}
                    <div class="row">
                        {% for product in page_obj %}
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 hover-scale">
                                    {% if product.image %}
                                        <img src="{{ product.image.url }}" class="card-img-top" 
                                             style="height: 180px; object-fit: cover;" alt="{{ product.name }}">
                                    {% else %}
                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" 
                                             style="height: 180px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="card-body">
                                        <h6 class="card-title">{{ product.name }}</h6>
                                        <p class="card-text small">
                                            {{ product.description|truncatewords:10 }}
                                        </p>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <strong class="text-success">${{ product.price }}</strong>
                                            {% if product.instock > 0 %}
                                                <span class="badge bg-success">In Stock</span>
                                            {% else %}
                                                <span class="badge bg-danger">Out of Stock</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <a href="{% url 'products:detail' product.pk %}" class="btn btn-sm btn-primary w-100">
                                            <i class="fas fa-eye"></i> View Product
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Products Pagination -->
                    {% if page_obj.has_other_pages %}
                        <nav aria-label="Products pagination">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                            <i class="fas fa-angle-left"></i> Previous
                                        </a>
                                    </li>
                                {% endif %}
                                
                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                            Next <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Products in this Category</h4>
                        <p class="text-muted">This category doesn't have any products yet.</p>
                        <a href="{% url 'products:create' %}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add First Product
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
