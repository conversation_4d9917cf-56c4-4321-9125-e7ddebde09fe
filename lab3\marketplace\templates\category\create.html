{% extends 'base.html' %}

{% block title %}Add New Category - MarketPlace{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'category:list' %}" class="text-decoration-none">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Add New Category</li>
            </ol>
        </nav>
        
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-plus-circle"></i> Add New Category
                </h2>
                <p class="text-muted mb-0">Create a new category to organize your products</p>
            </div>
            
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" id="categoryForm">
                    {% csrf_token %}
                    
                    <!-- Display form errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <!-- Category Name -->
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            <i class="fas fa-tag"></i> {{ form.name.label }} *
                        </label>
                        {{ form.name }}
                        {% if form.name.help_text %}
                            <div class="form-text">{{ form.name.help_text }}</div>
                        {% endif %}
                        {% if form.name.errors %}
                            <div class="text-danger small">
                                {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Category Description -->
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left"></i> {{ form.description.label }} *
                        </label>
                        {{ form.description }}
                        {% if form.description.help_text %}
                            <div class="form-text">{{ form.description.help_text }}</div>
                        {% endif %}
                        {% if form.description.errors %}
                            <div class="text-danger small">
                                {% for error in form.description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Category Image -->
                    <div class="mb-3">
                        <label for="{{ form.image.id_for_label }}" class="form-label">
                            <i class="fas fa-image"></i> {{ form.image.label }}
                        </label>
                        {{ form.image }}
                        {% if form.image.help_text %}
                            <div class="form-text">{{ form.image.help_text }}</div>
                        {% endif %}
                        {% if form.image.errors %}
                            <div class="text-danger small">
                                {% for error in form.image.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Image Preview -->
                    <div class="mb-3" id="imagePreview" style="display: none;">
                        <label class="form-label">Image Preview</label>
                        <div class="border rounded p-2">
                            <img id="preview" src="" alt="Preview" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'category:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Create Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> Category Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb"></i> Naming Tips</h6>
                        <ul class="small text-muted">
                            <li>Use clear, descriptive names</li>
                            <li>Keep names concise but meaningful</li>
                            <li>Avoid special characters</li>
                            <li>Use title case (e.g., "Electronics")</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-image"></i> Image Guidelines</h6>
                        <ul class="small text-muted">
                            <li>Use high-quality images</li>
                            <li>Recommended size: 400x300px</li>
                            <li>Supported formats: JPG, PNG, GIF</li>
                            <li>Keep file size under 2MB</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Image preview functionality
document.getElementById('{{ form.image.id_for_label }}').addEventListener('change', function(e) {
    const preview = document.getElementById('preview');
    const previewContainer = document.getElementById('imagePreview');
    
    if (e.target.files && e.target.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        }
        
        reader.readAsDataURL(e.target.files[0]);
    } else {
        previewContainer.style.display = 'none';
    }
});

// Form validation
document.getElementById('categoryForm').addEventListener('submit', function(e) {
    const name = document.getElementById('{{ form.name.id_for_label }}').value.trim();
    const description = document.getElementById('{{ form.description.id_for_label }}').value.trim();
    
    if (!name || !description) {
        e.preventDefault();
        alert('Please fill in all required fields!');
        return false;
    }
});
</script>
{% endblock %}
