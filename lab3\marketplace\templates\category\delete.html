{% extends 'base.html' %}

{% block title %}Delete {{ category.name }} - MarketPlace{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'category:list' %}" class="text-decoration-none">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'category:detail' category.pk %}" class="text-decoration-none">
                        {{ category.name }}
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Delete</li>
            </ol>
        </nav>
        
        <!-- Warning Card -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h2 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
                </h2>
            </div>
            
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h5 class="alert-heading">
                        <i class="fas fa-warning"></i> Warning!
                    </h5>
                    <p class="mb-0">
                        You are about to permanently delete this category and ALL products within it. This action cannot be undone.
                    </p>
                </div>
                
                <!-- Category Preview -->
                <div class="row">
                    <div class="col-md-4">
                        {% if category.image %}
                            <img src="{{ category.image.url }}" class="img-fluid rounded" 
                                 style="max-height: 200px; object-fit: cover;" alt="{{ category.name }}">
                        {% else %}
                            <div class="d-flex align-items-center justify-content-center bg-light rounded" 
                                 style="height: 200px;">
                                <i class="fas fa-tag fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-8">
                        <h3 class="text-danger">{{ category.name }}</h3>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Products Count:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-warning">{{ category.get_products_count }} product{{ category.get_products_count|pluralize }}</span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Created:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ category.created_at|date:"M d, Y H:i" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Last Updated:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ category.updated_at|date:"M d, Y H:i" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Description:</strong>
                            </div>
                            <div class="col-sm-8">
                                <p class="text-muted">{{ category.description|truncatewords:20 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Impact Warning -->
                {% if category.get_products_count > 0 %}
                    <div class="alert alert-warning mt-4" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-circle"></i> Impact Warning
                        </h6>
                        <p class="mb-0">
                            This category contains <strong>{{ category.get_products_count }} product{{ category.get_products_count|pluralize }}</strong>. 
                            Deleting this category will also delete all these products permanently.
                        </p>
                    </div>
                {% endif %}
                
                <!-- Confirmation Form -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h5><i class="fas fa-question-circle"></i> Are you sure you want to delete this category?</h5>
                    <p class="text-muted mb-3">
                        This will permanently remove "{{ category.name }}" and all its products from your marketplace. 
                        All associated data will be lost.
                    </p>
                    
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'category:detail' category.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <a href="{% url 'category:list' %}" class="btn btn-info">
                                <i class="fas fa-list"></i> Back to Categories
                            </a>
                            <button type="submit" class="btn btn-danger" onclick="return confirmDelete()">
                                <i class="fas fa-trash"></i> Yes, Delete Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> What happens when you delete this category?
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-danger">
                            <i class="fas fa-times-circle"></i> Will be removed:
                        </h6>
                        <ul class="text-muted">
                            <li>Category information and description</li>
                            <li>Category image and media files</li>
                            <li>ALL products in this category</li>
                            <li>Product images and data</li>
                            <li>Creation and update timestamps</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">
                            <i class="fas fa-lightbulb"></i> Alternative actions:
                        </h6>
                        <ul class="text-muted">
                            <li>Move products to another category first</li>
                            <li>Update category information instead</li>
                            <li>Archive the category for future reference</li>
                            <li>Export category and product data before deletion</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const productCount = {{ category.get_products_count }};
    let message = 'Are you absolutely sure you want to delete "{{ category.name }}"?';
    
    if (productCount > 0) {
        message += '\n\nThis will also delete ' + productCount + ' product' + (productCount > 1 ? 's' : '') + ' in this category.';
    }
    
    message += '\n\nThis action cannot be undone!';
    
    return confirm(message);
}

// Add some visual feedback
document.addEventListener('DOMContentLoaded', function() {
    const deleteButton = document.querySelector('button[type="submit"]');
    if (deleteButton) {
        deleteButton.addEventListener('mouseenter', function() {
            this.innerHTML = '<i class="fas fa-skull-crossbones"></i> Permanently Delete';
        });
        
        deleteButton.addEventListener('mouseleave', function() {
            this.innerHTML = '<i class="fas fa-trash"></i> Yes, Delete Category';
        });
    }
});
</script>
{% endblock %}
