{% extends 'base.html' %}

{% block title %}Products - MarketPlace{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title">
            <i class="fas fa-box"></i> Our Products
        </h1>
        
        <!-- Search and Add Product Section -->
        <div class="row mb-4">
            <div class="col-md-8">
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" 
                           placeholder="Search products by name, code, or description..." 
                           value="{{ search_query|default:'' }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'products:create' %}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add New Product
                </a>
            </div>
        </div>
        
        <!-- Products Count -->
        <div class="mb-3">
            <p class="text-muted">
                <i class="fas fa-info-circle"></i> 
                Showing {{ total_products }} product{{ total_products|pluralize }}
                {% if search_query %}for "{{ search_query }}"{% endif %}
            </p>
        </div>
        
        <!-- Products Grid -->
        {% if page_obj %}
            <div class="row">
                {% for product in page_obj %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" class="card-img-top" 
                                     style="height: 200px; object-fit: cover;" alt="{{ product.name }}">
                            {% else %}
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" 
                                     style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ product.name }}</h5>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-barcode"></i> {{ product.code }}
                                    <span class="ms-2">
                                        <a href="{% url 'category:detail' product.category.pk %}" class="badge bg-primary text-decoration-none">
                                            <i class="fas fa-tag"></i> {{ product.category.name }}
                                        </a>
                                    </span>
                                </p>
                                <p class="card-text flex-grow-1">
                                    {{ product.description|truncatewords:15 }}
                                </p>
                                
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong class="text-success">
                                            <i class="fas fa-dollar-sign"></i> ${{ product.price }}
                                        </strong>
                                    </div>
                                    <div class="col-6 text-end">
                                        {% if product.instock > 0 %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> {{ product.instock }} in stock
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times"></i> Out of stock
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="{% url 'products:detail' product.pk %}" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> Added {{ product.created_at|date:"M d, Y" }}
                                </small>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i> First
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    <i class="fas fa-angle-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    Next <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    Last <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">No Products Found</h3>
                {% if search_query %}
                    <p class="text-muted">No products match your search for "{{ search_query }}"</p>
                    <a href="{% url 'products:list' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> View All Products
                    </a>
                {% else %}
                    <p class="text-muted">Start by adding your first product!</p>
                    <a href="{% url 'products:create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add First Product
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
